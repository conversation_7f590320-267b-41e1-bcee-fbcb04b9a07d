import { useCallback, useEffect, useRef, useState } from "react";
import { Check, Edit3, X } from "lucide-react";
import { type ControllerRenderProps, type FieldValues } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { useFormSubmit } from "@/utils/form";
import { useFormContext } from "../formContext";
import type { BaseFormFieldProps } from "../types";

export type InlineEditTextFieldProps<T extends FieldValues> = Omit<BaseFormFieldProps<T>, "name" | "label"> & {
    startAdornment?: React.ReactNode;
    endAdornment?: React.ReactNode;
} & React.InputHTMLAttributes<HTMLInputElement>;

export default function InlineEditText<T extends FieldValues>({
    startAdornment,
    endAdornment,
    field,
    readonly,
    ...rest
}: InlineEditTextFieldProps<T> & { field: ControllerRenderProps<T> }) {
    const [isEditing, setIsEditing] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [originalValue, setOriginalValue] = useState<string>("");
    const editingRef = useRef<HTMLDivElement>(null);
    const { errors } = useFormContext<T>();
    const [, submitForm] = useFormSubmit(editingRef);

    const handleClick = () => {
        if (readonly) return;

        setIsEditing(true);
        setOriginalValue(field.value || "");
    };

    const handleCancel = () => {
        field.onChange(originalValue);
        setIsEditing(false);
    };

    const handleSave = useCallback(async () => {
        if (isSaving) return;
        setIsSaving(true);
        try {
            submitForm();

            if (!errors || Object.keys(errors).length === 0) {
                setIsEditing(false);
            }
        } finally {
            setIsSaving(false);
        }
    }, [isSaving, submitForm, errors]);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (isEditing && editingRef.current && !editingRef.current.contains(event.target as Node)) {
                handleSave();
            }
        };

        if (isEditing) {
            document.addEventListener("mousedown", handleClickOutside);
        }

        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [isEditing, handleSave]);

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === "Enter") {
            e.preventDefault();
            handleSave();
        } else if (e.key === "Escape") {
            handleCancel();
        }
    };

    if (isEditing) {
        return (
            <div ref={editingRef} className="relative mb-0.5 flex items-center gap-1">
                {startAdornment && (
                    <div className="text-muted-foreground pointer-events-none absolute inset-y-0 left-0 z-10 flex items-center pl-3">
                        {startAdornment}
                    </div>
                )}
                <Input
                    {...rest}
                    {...field}
                    autoFocus
                    onKeyDown={handleKeyDown}
                    className={[startAdornment ? "pl-9" : "", "pr-16"].filter(Boolean).join(" ")}
                />
                <div className="absolute inset-y-0 right-1 flex items-center gap-1">
                    <Button
                        type="button"
                        size="sm"
                        variant="ghost"
                        onClick={handleSave}
                        disabled={isSaving}
                        className="h-7 w-7 p-0"
                    >
                        <Check className="h-3 w-3" />
                    </Button>
                    <Button
                        type="button"
                        size="sm"
                        variant="ghost"
                        onClick={handleCancel}
                        disabled={isSaving}
                        className="h-7 w-7 p-0"
                    >
                        <X className="h-3 w-3" />
                    </Button>
                </div>
            </div>
        );
    }

    const displayText = field.value || rest.placeholder || "Click to edit";
    const hasValue = Boolean(field.value);

    return (
        <div
            className={`group relative rounded-md border border-transparent px-3 py-2 text-sm transition-colors ${
                readonly ? "cursor-default" : "hover:border-input hover:bg-muted/50 cursor-pointer"
            }`}
            onClick={handleClick}
        >
            <div className="flex items-center justify-between">
                <div className="flex min-w-0 flex-1 items-center gap-2">
                    {startAdornment && (
                        <div className="text-muted-foreground flex flex-shrink-0 items-center">{startAdornment}</div>
                    )}
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <span className={`truncate ${hasValue ? "" : "text-muted-foreground"}`}>{displayText}</span>
                        </TooltipTrigger>
                        {hasValue && displayText.length > 30 && (
                            <TooltipContent>
                                <p className="max-w-xs break-words">{displayText}</p>
                            </TooltipContent>
                        )}
                    </Tooltip>
                    {endAdornment && (
                        <div className="text-muted-foreground flex flex-shrink-0 items-center">{endAdornment}</div>
                    )}
                </div>
                {!readonly && (
                    <Edit3 className="text-muted-foreground h-3 w-3 flex-shrink-0 opacity-0 transition-opacity group-hover:opacity-100" />
                )}
            </div>
        </div>
    );
}
