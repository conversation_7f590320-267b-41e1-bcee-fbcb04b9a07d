import type { FieldValues } from "react-hook-form";
import { Controller } from "react-hook-form";
import { editModes } from "../consts";
import { useFormContext } from "../formContext";
import { FormField } from "../formField";
import type { BaseFormFieldProps } from "../types";
import ClassicEditText from "./classicEditText";
import InlineEditText from "./inlineEditText";

export type TextFieldProps<T extends FieldValues> = BaseFormFieldProps<T> & {
    startAdornment?: React.ReactNode;
    endAdornment?: React.ReactNode;
} & React.InputHTMLAttributes<HTMLInputElement>;

export function Text<T extends FieldValues>({
    name,
    label,
    startAdornment,
    endAdornment,
    readonly,
    ...rest
}: TextFieldProps<T>) {
    const { editMode, control, errors } = useFormContext<T>();

    return (
        <Controller
            name={name}
            control={control}
            render={({ field }) => (
                <FormField label={label} error={errors[name]?.message}>
                    {editMode === editModes.CLASSIC && (
                        <ClassicEditText
                            {...rest}
                            field={field}
                            startAdornment={startAdornment}
                            endAdornment={endAdornment}
                            readonly={readonly}
                        />
                    )}
                    {editMode === editModes.INLINE && (
                        <InlineEditText
                            {...rest}
                            field={field}
                            startAdornment={startAdornment}
                            endAdornment={endAdornment}
                            readonly={readonly}
                        />
                    )}
                </FormField>
            )}
        />
    );
}

export function Adornment({ interactive, children }: { interactive: boolean; children: React.ReactNode }) {
    return <div className={interactive ? "pointer-events-auto cursor-pointer" : ""}>{children}</div>;
}
