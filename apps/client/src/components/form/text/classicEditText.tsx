import type { ControllerRenderProps, FieldValues } from "react-hook-form";
import { Input } from "@/components/ui/input";
import type { BaseFormFieldProps } from "../types";

export type ClassicEditTextFieldProps<T extends FieldValues> = Omit<BaseFormFieldProps<T>, "name" | "label"> & {
    startAdornment?: React.ReactNode;
    endAdornment?: React.ReactNode;
} & React.InputHTMLAttributes<HTMLInputElement>;

export default function ClassicEditText<T extends FieldValues>({
    startAdornment,
    endAdornment,
    field,
    readonly,
    ...rest
}: ClassicEditTextFieldProps<T> & { field: ControllerRenderProps<T> }) {
    return (
        <div className="relative">
            {startAdornment && (
                <div className="text-muted-foreground pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    {startAdornment}
                </div>
            )}
            <Input
                {...rest}
                {...field}
                disabled={readonly}
                className={[startAdornment ? "pl-9" : "", endAdornment ? "pr-9" : ""].filter(Boolean).join(" ")}
            />
            {endAdornment && (
                <div className="text-muted-foreground pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                    {endAdornment}
                </div>
            )}
        </div>
    );
}
